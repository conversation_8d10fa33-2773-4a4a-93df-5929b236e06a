import os
import numpy as np
import torch
import PIL.Image as pil
from torchvision import transforms
import cv2
import argparse

from layers import disp_to_depth
import networks

def calculate_object_volume(depth_map, pixel_area_threshold=100, depth_threshold=0.1):
    """
    计算物体体积
    Args:
        depth_map: 深度图 (numpy array)
        pixel_area_threshold: 最小像素区域阈值
        depth_threshold: 深度变化阈值，用于分割物体
    Returns:
        volume: 估算的体积
        object_mask: 物体掩码
    """
    # 简单的物体分割：基于深度变化
    depth_grad = np.gradient(depth_map)
    grad_magnitude = np.sqrt(depth_grad[0]**2 + depth_grad[1]**2)
    
    # 创建物体掩码
    object_mask = grad_magnitude > depth_threshold
    
    # 形态学操作清理掩码
    kernel = np.ones((5,5), np.uint8)
    object_mask = cv2.morphologyEx(object_mask.astype(np.uint8), cv2.MORPH_CLOSE, kernel)
    object_mask = cv2.morphologyEx(object_mask, cv2.MORPH_OPEN, kernel)
    
    # 找到连通区域
    contours, _ = cv2.findContours(object_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    total_volume = 0
    for contour in contours:
        area = cv2.contourArea(contour)
        if area > pixel_area_threshold:
            # 创建单个物体的掩码
            single_mask = np.zeros_like(object_mask)
            cv2.fillPoly(single_mask, [contour], 1)
            
            # 计算该物体的体积（简化方法）
            object_depths = depth_map[single_mask == 1]
            if len(object_depths) > 0:
                # 假设每个像素代表1cm²的面积（需要根据实际相机参数调整）
                pixel_area = 0.01  # 1cm² per pixel
                avg_depth = np.mean(object_depths)
                volume = area * pixel_area * avg_depth
                total_volume += volume
    
    return total_volume, object_mask

def process_images_for_volume(weight_folder, image_folder, model_name="lite-mono"):
    """
    处理图像文件夹中的所有图像并计算体积
    """
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 加载模型
    encoder_path = os.path.join(weight_folder, "encoder.pth")
    decoder_path = os.path.join(weight_folder, "depth.pth")
    
    encoder_dict = torch.load(encoder_path, map_location=device)
    decoder_dict = torch.load(decoder_path, map_location=device)
    
    feed_height = encoder_dict['height']
    feed_width = encoder_dict['width']
    
    # 初始化模型
    encoder = networks.LiteMono(model=model_name, height=feed_height, width=feed_width)
    depth_decoder = networks.DepthDecoder(encoder.num_ch_enc, scales=range(3))
    
    model_dict = encoder.state_dict()
    depth_model_dict = depth_decoder.state_dict()
    encoder.load_state_dict({k: v for k, v in encoder_dict.items() if k in model_dict})
    depth_decoder.load_state_dict({k: v for k, v in decoder_dict.items() if k in depth_model_dict})
    
    encoder.to(device)
    depth_decoder.to(device)
    encoder.eval()
    depth_decoder.eval()
    
    # 获取图像文件
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
    image_paths = []
    for ext in image_extensions:
        image_paths.extend([os.path.join(image_folder, f) for f in os.listdir(image_folder) 
                           if f.lower().endswith(ext)])
    
    results = []
    
    with torch.no_grad():
        for image_path in image_paths:
            print(f"处理图像: {image_path}")
            
            # 加载和预处理图像
            input_image = pil.open(image_path).convert('RGB')
            original_width, original_height = input_image.size
            input_image_resized = input_image.resize((feed_width, feed_height), pil.LANCZOS)
            input_tensor = transforms.ToTensor()(input_image_resized).unsqueeze(0).to(device)
            
            # 深度预测
            features = encoder(input_tensor)
            outputs = depth_decoder(features)
            disp = outputs[("disp", 0)]
            
            # 转换为深度
            _, depth = disp_to_depth(disp, 0.1, 100)
            
            # 调整到原始尺寸
            depth_resized = torch.nn.functional.interpolate(
                depth, (original_height, original_width), mode="bilinear", align_corners=False)
            
            depth_np = depth_resized.squeeze().cpu().numpy()
            
            # 计算体积
            volume, object_mask = calculate_object_volume(depth_np)
            
            # 保存结果
            base_name = os.path.splitext(os.path.basename(image_path))[0]
            
            # 保存深度图
            depth_output_path = os.path.join(image_folder, f"{base_name}_depth.npy")
            np.save(depth_output_path, depth_np)
            
            # 保存可视化深度图
            depth_vis = (depth_np - depth_np.min()) / (depth_np.max() - depth_np.min()) * 255
            depth_vis_path = os.path.join(image_folder, f"{base_name}_depth_vis.png")
            cv2.imwrite(depth_vis_path, depth_vis.astype(np.uint8))
            
            # 保存物体掩码
            mask_path = os.path.join(image_folder, f"{base_name}_mask.png")
            cv2.imwrite(mask_path, object_mask * 255)
            
            result = {
                'image_path': image_path,
                'estimated_volume': volume,
                'depth_map_path': depth_output_path,
                'visualization_path': depth_vis_path,
                'mask_path': mask_path
            }
            results.append(result)
            
            print(f"估算体积: {volume:.4f} 立方米")
    
    return results

if __name__ == "__main__":
    weight_folder = r"D:\Code\Work\Lite-Mono-main\weight"
    image_folder = r"D:\Code\Work\Lite-Mono-main\datasets\2025-07-18"
    
    results = process_images_for_volume(weight_folder, image_folder)
    
    # 打印所有结果
    print("\n=== 体积估算结果 ===")
    for result in results:
        print(f"图像: {os.path.basename(result['image_path'])}")
        print(f"估算体积: {result['estimated_volume']:.4f} 立方米")
        print("-" * 50)